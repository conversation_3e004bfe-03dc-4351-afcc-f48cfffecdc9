<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\UnloadingPointValidity;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method UnloadingPointValidity|null find($id, $lockMode = null, $lockVersion = null)
 * @method UnloadingPointValidity|null findOneBy(array $criteria, array $orderBy = null)
 * @method UnloadingPointValidity[]    findAll()
 * @method UnloadingPointValidity[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 *
 * @extends ServiceEntityRepository<UnloadingPointValidity>
 */
class UnloadingPointValidityRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UnloadingPointValidity::class);
    }

    // /**
    //  * @return UnloadingPointValidity[] Returns an array of UnloadingPointValidity objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('c.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?UnloadingPointValidity
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */

    public function getValidity($unloadingPoint, $week): mixed
    {
        $qb = $this->createQueryBuilder(alias: 'v')
            ->join(join: 'v.unloadingPoint', alias: 'u')
            ->where('u.id = :unloadingPoint')
            ->andWhere('v.validFrom <= :valid_from')
            ->andWhere('v.validTo >= :valid_to');

        $qb->setParameter(key: 'unloadingPoint', value: $unloadingPoint)
            ->setParameter(key: 'valid_from', value: $week[0]->getDtObject())
            ->setParameter(key: 'valid_to', value: end(array: $week)->getDtObject());
        $qb->setMaxResults(maxResults: 1);

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function getValidities($unloadingPoint, $week): mixed
    {
        $qb = $this->createQueryBuilder(alias: 'v')
            ->join(join: 'v.unloadingPoint', alias: 'u')
            ->where('u.id = :unloadingPoint')
            ->andWhere('v.validFrom <= :valid_from')
            ->andWhere('v.validTo >= :valid_to');

        $qb->setParameter(key: 'unloadingPoint', value: $unloadingPoint)
        ->setParameter(key: 'valid_from', value: end(array: $week)->getDtObject())
        ->setParameter(key: 'valid_to', value: $week[0]->getDtObject());

        return $qb->getQuery()->getResult();
    }

    public function getValidityCount($unloadingPoint, $week): mixed
    {
        $qb = $this->createQueryBuilder(alias: 'v')
            ->join(join: 'v.unloadingPoint', alias: 'u')
            ->where('u.id = :unloadingPoint')
            ->andWhere('v.validFrom <= :valid_from')
            ->andWhere('v.validTo >= :valid_to');

        $qb->setParameter(key: 'unloadingPoint', value: $unloadingPoint)
            ->setParameter(key: 'valid_from', value: $week[0]->getDtObject())
            ->setParameter(key: 'valid_to', value: end(array: $week)->getDtObject());
        $qb->setMaxResults(maxResults: 1);

        return $qb->getQuery()->getOneOrNullResult();
    }
}
