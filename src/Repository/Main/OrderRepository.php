<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\ContractArea;
use App\Entity\Main\Order;
use App\Entity\Main\UnloadingPoint;
use App\Entity\Main\User;
use App\Services\AccessHelper;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

/**
 * Class OrderRepository.
 *
 * @extends ServiceEntityRepository<Order>
 */
class OrderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry, protected TokenStorageInterface $tokenStorage, protected AccessHelper $accessHelper)
    {
        parent::__construct($registry, Order::class);
    }

    /**
     * @throws \Exception
     */
    public function findInBetweenQuery(
        \DateTime $startDate,
        \DateTime $endDate,
        ?CollectingPlace $collectingPlace = null,
        ?ContractArea $contractArea = null,
    ): QueryBuilder {
        /** @var User $user */
        $user = $this->tokenStorage->getToken()->getUser();

        $qb = $this->createQueryBuilder(alias: 'o')
            ->addSelect('o.id as id, o.id+'.Order::DISPOSAL_OFFSET.' as disposalNumber')
            ->addSelect('o.date as date, o.transfered as transfered, o.canceled as canceled')
            ->addSelect('o.driverMessage as driverMessage, o.dispoMessage as dispoMessage')
            ->addSelect('ca.name as contractArea')
            ->addSelect('sp.name as providerName')

            ->addSelect('c.dsdId as dsdid, c.name1 as name1, c.name2 as name2')
            ->addSelect('c.street as street, c.houseNumber as houseNumber')
            ->addSelect('c.postalCode as postalCode, c.city as city')
            ->join(join: 'o.collectingPlace', alias: 'c')
            ->join(join: 'o.systemProvider', alias: 'sp')
            ->join(join: 'o.contractArea', alias: 'ca')
            ->where('o.date >= :startDate')
            ->andWhere('o.date <= :endDate');

        $qb->setParameter(key: 'startDate', value: new \DateTime(datetime: $startDate->format(format: 'Y-m-d')))
            ->setParameter(key: 'endDate', value: new \DateTime(datetime: $endDate->format(format: 'Y-m-d')));

        $conditions = [];
        $contractAreas = $this->accessHelper->getContractAreaList(user: $user);

        foreach ($contractAreas as $key => $area) {
            if ($contractArea && $contractArea != $area) {
                continue;
            }

            $conditions[] = 'o.contractArea = :contractArea'.$key;
            $qb->setParameter(key: 'contractArea'.$key, value: $area);
        }

        if ([] !== $conditions) {
            $qb->andWhere($qb->expr()->orX()->addMultiple(args: $conditions));
        } else {
            $qb->andWhere('o.contractArea = :contractAreaId');
            $qb->setParameter(key: 'contractAreaId', value: 0);
        }

        $conditions = [];
        $collectingPlaces = $this->accessHelper->getCollectingPlaceList(user: $user);
        foreach ($collectingPlaces as $key => $place) {
            if ($collectingPlace && $collectingPlace != $place) {
                continue;
            }

            $conditions[] = 'o.collectingPlace = :collectingPlace'.$key;
            $qb->setParameter(key: 'collectingPlace'.$key, value: $place);
        }

        if ([] !== $conditions) {
            $qb->andWhere($qb->expr()->orX()->addMultiple(args: $conditions));
        } else {
            $qb->andWhere('o.collectingPlace = :collectingPlaceId');
            $qb->setParameter(key: 'collectingPlaceId', value: 0);
        }

        return $qb;
    }

    public function reportUnloadingPointQuery(
        \DateTime $startDate,
        \DateTime $endDate,
        ?UnloadingPoint $unloadingPoint = null,
    ): QueryBuilder {
        $qb = $this->createQueryBuilder(alias: 'o')
            ->addSelect('o.id as id, o.id+'.Order::DISPOSAL_OFFSET.' as disposalNumber')
            ->addSelect('o.date as date, o.transfered as transfered, o.canceled as canceled, o.transferStatus as transferStatus')
            ->addSelect('o.driverMessage as driverMessage, o.dispoMessage as dispoMessage')
            ->addSelect('ca.name as contractArea')
            ->addSelect('sp.name as providerName')

            // collecting place
            ->addSelect('co.dsdId as cp_dsdid, co.name1 as cp_name1, co.name2 as cp_name2')
            ->addSelect('co.street as cp_street, co.houseNumber as cp_houseNumber')
            ->addSelect('co.postalCode as cp_postalCode, co.city as cp_city')

            // unloading point
            ->addSelect('ul.dsdId as ul_dsdid, ul.name1 as ul_name1, ul.name2 as ul_name2')
            ->addSelect('ul.street as ul_street, ul.houseNumber as ul_houseNumber')
            ->addSelect('ul.postalCode as ul_postalCode, ul.city as ul_city')

            ->join(join: 'o.collectingPlace', alias: 'co')
            ->join(join: 'o.unloadingPoint', alias: 'ul')
            ->join(join: 'o.systemProvider', alias: 'sp')
            ->join(join: 'o.contractArea', alias: 'ca')

            ->where('o.date >= :startDate')
            ->andWhere('o.date <= :endDate');

        $qb->setParameter(key: 'startDate', value: new \DateTime(datetime: $startDate->format(format: 'Y-m-d')))
            ->setParameter(key: 'endDate', value: new \DateTime(datetime: $endDate->format(format: 'Y-m-d')));

        if ($unloadingPoint instanceof UnloadingPoint) {
            $qb->andWhere('o.unloadingPoint = :unloadingPoint')
                ->setParameter(key: 'unloadingPoint', value: $unloadingPoint);
        }

        return $qb;
    }

    public function findInBetween(
        \DateTime $startDate,
        \DateTime $endDate,
        CollectingPlace $collectingPlace,
        ContractArea $contractArea,
    ): array {
        $qb = $this->createQueryBuilder(alias: 'o')
            ->join(join: 'o.collectingPlace', alias: 'c')
            ->where('o.date >= :startDate')
            ->andWhere('o.date <= :endDate')
            ->andWhere('o.contractArea = :contractArea')
            ->andWhere('c.id = :collectingPlace');

        $qb->setParameter(key: 'startDate', value: $startDate)
            ->setParameter(key: 'endDate', value: $endDate)
            ->setParameter(key: 'contractArea', value: $contractArea)
            ->setParameter(key: 'collectingPlace', value: $collectingPlace);

        return $qb->getQuery()->getResult();
    }

    public function findTransferOpenInBetween(
        \DateTime $startDate,
        \DateTime $endDate,
    ): array {
        $qb = $this->createQueryBuilder(alias: 'o')
            ->where('o.date >= :startDate')
            ->andWhere('o.date <= :endDate')
            ->andWhere('o.transferStatus = :status OR o.transferStatus = :errorStatus')
            ->andWhere('o.canceled IS NULL');

        $qb->setParameter(key: 'startDate', value: $startDate)
            ->setParameter(key: 'endDate', value: $endDate)
            ->setParameter(key: 'status', value: Order::STATUS_TRANSMISSION_READY)
            ->setParameter(key: 'errorStatus', value: Order::STATUS_TRANSMISSION_ERROR);

        return $qb->getQuery()->getResult();
    }

    public function getOrderCountByArea(
        \DateTime $startDate,
        \DateTime $endDate,
        ContractArea $contractArea,
    ): int {
        $qb = $this->createQueryBuilder(alias: 'o')
            ->select('count(o.id)')
            ->where('o.date >= :startDate')
            ->andWhere('o.date <= :endDate')
            ->andWhere('o.contractArea = :contractArea')
            ->andWhere('o.canceled IS NULL');

        $qb->setParameter(key: 'startDate', value: $startDate)
            ->setParameter(key: 'endDate', value: $endDate)
            ->setParameter(key: 'contractArea', value: $contractArea);

        return $qb->getQuery()->getSingleScalarResult();
    }
}
