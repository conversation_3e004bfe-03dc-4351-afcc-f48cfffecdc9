<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\ContractAreaValidity;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method ContractAreaValidity|null find($id, $lockMode = null, $lockVersion = null)
 * @method ContractAreaValidity|null findOneBy(array $criteria, array $orderBy = null)
 * @method ContractAreaValidity[]    findAll()
 * @method ContractAreaValidity[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 *
 * @extends ServiceEntityRepository<ContractAreaValidity>
 */
class ContractAreaValidityRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ContractAreaValidity::class);
    }

    // /**
    //  * @return ContractAreaValidity[] Returns an array of ContractAreaValidity objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('c.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?ContractAreaValidity
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */

    public function getValidity($contractArea, $week): mixed
    {
        $qb = $this->createQueryBuilder(alias: 'v')
            ->join(join: 'v.contractArea', alias: 'a')
            ->where('a.id = :contractArea')
            ->andWhere('v.validFrom <= :valid_from')
            ->andWhere('v.validTo >= :valid_to');

        $qb->setParameter(key: 'contractArea', value: $contractArea)
            ->setParameter(key: 'valid_from', value: $week[0]->getDtObject())
            ->setParameter(key: 'valid_to', value: end(array: $week)->getDtObject());
        $qb->setMaxResults(maxResults: 1);

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function getValidities($contractArea, $week): mixed
    {
        $qb = $this->createQueryBuilder(alias: 'v')
            ->join(join: 'v.contractArea', alias: 'a')
            ->where('a.id = :contractArea')
            ->andWhere('v.validFrom <= :valid_from')
            ->andWhere('v.validTo >= :valid_to');

        $qb->setParameter(key: 'contractArea', value: $contractArea)
        ->setParameter(key: 'valid_from', value: end(array: $week)->getDtObject())
        ->setParameter(key: 'valid_to', value: $week[0]->getDtObject());

        return $qb->getQuery()->getResult();
    }

    public function getValidityCount($contractArea, $week): mixed
    {
        $qb = $this->createQueryBuilder(alias: 'v')
            ->join(join: 'v.contractArea', alias: 'a')
            ->where('a.id = :contractArea')
            ->andWhere('v.validFrom <= :valid_from')
            ->andWhere('v.validTo >= :valid_to');

        $qb->setParameter(key: 'contractArea', value: $contractArea)
            ->setParameter(key: 'valid_from', value: $week[0]->getDtObject())
            ->setParameter(key: 'valid_to', value: end(array: $week)->getDtObject());
        $qb->setMaxResults(maxResults: 1);

        return $qb->getQuery()->getOneOrNullResult();
    }
}
