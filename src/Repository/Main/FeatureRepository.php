<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\Feature;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Feature|null find($id, $lockMode = null, $lockVersion = null)
 * @method Feature|null findOneBy(array $criteria, array $orderBy = null)
 * @method Feature[]    findAll()
 * @method Feature[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 *
 * <<<<<<< HEAD
 *
 * @extends ServiceEntityRepository<Feature>
 * =======
 * @extends ServiceEntityRepository<Feature>
 * >>>>>>> master
 */
class FeatureRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Feature::class);
    }
}
