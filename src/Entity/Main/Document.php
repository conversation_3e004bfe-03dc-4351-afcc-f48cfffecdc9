<?php

declare(strict_types=1);

namespace App\Entity\Main;

use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Repository\Main\DocumentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;

// @ORM\Entity(repositoryClass=DocumentRepository::class)
#[ORM\Entity(repositoryClass: DocumentRepository::class)]
class Document implements HasUuid, HasCreateModifyStamps
{
    use CommonTrait;

    #[ORM\Column(type: Types::STRING, length: 255)]
    #[Groups(['DocumentWrite', 'DocumentRead', 'list'])]
    private ?string $number = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $date = null;

    #[ORM\Column(type: Types::FLOAT, length: 255)]
    #[Groups(['DocumentWrite', 'DocumentRead'])]
    private ?float $amount = null;

    #[ORM\Column(type: Types::STRING, length: 255)]
    #[Groups(['DocumentWrite', 'DocumentRead'])]
    private ?string $unit = null;

    #[ORM\Column(type: Types::BOOLEAN)]
    #[Groups(['DocumentWrite', 'DocumentRead'])]
    private ?bool $visible = null;

    #[ORM\Column(type: Types::BOOLEAN)]
    #[Groups(['DocumentWrite', 'DocumentRead'])]
    private ?bool $active = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private $additionals = [];

    #[ORM\ManyToOne(targetEntity: DocumentType::class, inversedBy: 'documents')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['DocumentWrite', 'DocumentRead', 'list'])]
    private ?DocumentType $documentType = null;

    #[ORM\ManyToOne(targetEntity: Contract::class, inversedBy: 'documents')]
    #[Groups(['DocumentWrite', 'DocumentRead', 'list'])]
    private ?Contract $contract = null;

    public function getNumber(): ?string
    {
        return $this->number;
    }

    public function setNumber(string $number): self
    {
        $this->number = $number;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getUnit(): ?string
    {
        return $this->unit;
    }

    public function setUnit(string $unit): self
    {
        $this->unit = $unit;

        return $this;
    }

    public function getVisible(): ?bool
    {
        return $this->visible;
    }

    public function setVisible(bool $visible): self
    {
        $this->visible = $visible;

        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getAdditionals(): ?array
    {
        return $this->additionals;
    }

    public function setAdditionals(?array $additionals): self
    {
        $this->additionals = $additionals;

        return $this;
    }

    public function getDocumentType(): ?DocumentType
    {
        return $this->documentType;
    }

    public function setDocumentType(?DocumentType $documentType): self
    {
        $this->documentType = $documentType;

        return $this;
    }

    public function getContract(): ?Contract
    {
        return $this->contract;
    }

    public function setContract(?Contract $contract): self
    {
        $this->contract = $contract;

        return $this;
    }
}
