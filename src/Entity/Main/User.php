<?php

declare(strict_types=1);

namespace App\Entity\Main;

use App\Entity\Common\CommonTrait;
use App\Entity\Common\DeletedTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasDeleted;
use App\Entity\Common\HasLocked;
use App\Entity\Common\HasUuid;
use App\Entity\Common\LockedTrait;
use App\Repository\Main\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Ramsey\Uuid\UuidInterface;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: '`user`')]
class User implements UserInterface, HasUuid, HasCreateModifyStamps, HasLocked, HasDeleted, PasswordAuthenticatedUserInterface, \Stringable
{
    use CommonTrait;
    use LockedTrait;
    use DeletedTrait;

    public static function create(): User
    {
        return new User();
    }

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING, length: 180, unique: true)]
    private ?string $email = null;

    #[ORM\Column(type: Types::JSON)]
    private $roles = [];

    /**
     * @var string The hashed password
     */
    #[ORM\Column(type: Types::STRING)]
    private ?string $password = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $passwordDate = null;

    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    private ?bool $resetPassword = null;

    /**
     * @var string The hashed password
     */
    #[ORM\Column(name: 'password_reset_hash', type: Types::STRING, length: 255, nullable: true)]
    private ?string $passwordResetHash = null;

    /**
     * @var Collection<int, UserAccess>
     */
    #[ORM\OneToMany(mappedBy: 'user', targetEntity: UserAccess::class, orphanRemoval: true)]
    private Collection $userAccessList;

    #[ORM\Column(type: Types::STRING, length: 10, nullable: false)]
    private string $locale = 'de';

    public function __construct(?UuidInterface $uuid = null)
    {
        $this->userAccessList = new ArrayCollection();
        $this->uuid = $uuid;

        $this->setRoles(roles: ['ROLE_ORDER', 'ROLE_DOCUMENTS']);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * @return $this
     */
    public function setEmail(string $email): static
    {
        $this->email = $email;

        return $this;
    }

    /**
     * A visual identifier that represents this user.
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string) $this->email;
    }

    /**
     * @see UserInterface
     */
    public function getRoles(): array
    {
        $roles = $this->roles;
        // guarantee every user at least has ROLE_USER
        $roles[] = 'ROLE_USER';

        return array_unique(array: $roles);
    }

    public function setRoles(array $roles): self
    {
        $this->roles = $roles;

        return $this;
    }

    /**
     * @see UserInterface
     */
    public function getPassword(): string
    {
        return (string) $this->password;
    }

    /**
     * @return $this
     */
    public function setPassword(string $password): static
    {
        $this->password = $password;

        return $this;
    }

    public function getPasswordResetHash(): string
    {
        return $this->passwordResetHash;
    }

    /**
     * @return $this
     */
    public function setPasswordResetHash(?string $passwordResetHash): static
    {
        $this->passwordResetHash = $passwordResetHash;

        return $this;
    }

    public function getPasswordDate(): ?\DateTimeInterface
    {
        return $this->passwordDate;
    }

    public function setPasswordDate(?\DateTimeInterface $passwordDate): User
    {
        $this->passwordDate = $passwordDate;

        return $this;
    }

    public function getResetPassword(): ?bool
    {
        return $this->resetPassword;
    }

    public function setResetPassword(?bool $resetPassword): self
    {
        $this->resetPassword = $resetPassword;

        return $this;
    }

    /**
     * @see UserInterface
     */
    public function getSalt(): ?string
    {
        return null;
        // not needed when using the "bcrypt" algorithm in security.yaml
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }

    public function __toString(): string
    {
        return $this->getEmail() ?? throw new \RuntimeException(message: 'No user loaded');
    }

    /**
     * @return Collection<int, UserAccess>
     */
    public function getUserAccessList(): Collection
    {
        return $this->userAccessList;
    }

    public function addUserAccess(UserAccess $userAccess): self
    {
        if (!$this->userAccessList->contains(element: $userAccess)) {
            $this->userAccessList->add(element: $userAccess);
        }

        return $this;
    }

    public function removeUserAccess(UserAccess $userAccess): self
    {
        $this->userAccessList->removeElement(element: $userAccess);

        return $this;
    }

    public function getUsername(): string
    {
        return (string) $this->email;
    }

    public function getLocale(): ?string
    {
        return $this->locale;
    }

    public function setLocale(string $locale): void
    {
        $this->locale = $locale;
    }
}
