<?php

declare(strict_types=1);

namespace App\Entity\Common;

use Ramsey\Uuid\UuidInterface;

interface HasCreateModifyStamps
{
    public function getModifiedBy(): ?UuidInterface;

    public function setModifiedBy(?UuidInterface $modifiedBy);

    public function getModifiedAt(): ?\DateTimeInterface;

    public function setModifiedAt(?\DateTime $modifiedAt);

    public function getCreatedBy(): ?UuidInterface;

    public function setCreatedBy(UuidInterface $createdBy);

    public function getCreatedAt(): \DateTimeInterface;

    public function setCreatedAt(\DateTime $createdAt);
}
