<?php

declare(strict_types=1);

namespace App\Entity\Files;

use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Repository\Files\DocumentDataRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

// @ORM\Entity(repositoryClass=DocumentDataRepository::class)
#[ORM\Table(options: ['engine' => 'MyISAM'])]
#[ORM\Entity(repositoryClass: DocumentDataRepository::class)]
class DocumentData implements HasUuid, HasCreateModifyStamps
{
    use CommonTrait;

    #[ORM\Column(type: Types::BLOB, nullable: true)]
    private $file;

    #[ORM\Column(type: Types::STRING, length: 63)]
    private ?string $mimeType = null;

    public function getFile()
    {
        return $this->file;
    }

    public function setFile($file): self
    {
        $this->file = $file;

        return $this;
    }

    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function setMimeType(string $mimeType): self
    {
        $this->mimeType = $mimeType;

        return $this;
    }
}
